import { useState, useCallback, useRef } from 'react';

interface HoverState {
  isVisible: boolean;
  userId: string | null;
  username: string | null;
  position: { x: number; y: number };
}

export const useUserHover = () => {
  const [hoverState, setHoverState] = useState<HoverState>({
    isVisible: false,
    userId: null,
    username: null,
    position: { x: 0, y: 0 }
  });

  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const showHoverCard = useCallback((
    userId: string,
    username: string,
    event: React.MouseEvent
  ) => {
    // Clear any existing timeouts
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
    }

    // Get mouse position
    const x = event.clientX;
    const y = event.clientY;

    // Show hover card after a short delay
    hoverTimeoutRef.current = setTimeout(() => {
      setHoverState({
        isVisible: true,
        userId,
        username,
        position: { x, y }
      });
    }, 500); // 500ms delay before showing
  }, []);

  const hideHoverCard = useCallback(() => {
    // Clear show timeout if it exists
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }

    // Hide after a short delay to allow moving to the card
    hideTimeoutRef.current = setTimeout(() => {
      setHoverState({
        isVisible: false,
        userId: null,
        username: null,
        position: { x: 0, y: 0 }
      });
    }, 100); // 100ms delay before hiding
  }, []);

  const immediateHide = useCallback(() => {
    // Clear all timeouts
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
    }

    // Hide immediately
    setHoverState({
      isVisible: false,
      userId: null,
      username: null,
      position: { x: 0, y: 0 }
    });
  }, []);

  const cancelHide = useCallback(() => {
    // Cancel hide timeout when mouse enters the card
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
    }
  }, []);

  return {
    hoverState,
    showHoverCard,
    hideHoverCard,
    immediateHide,
    cancelHide
  };
};
