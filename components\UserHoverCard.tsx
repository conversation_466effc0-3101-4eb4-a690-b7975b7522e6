import React, { useState, useEffect } from 'react';
import { User } from '../types';
import { useAuth } from '../hooks/useAuth';
import { usePosts } from '../hooks/usePosts';

interface UserHoverCardProps {
  userId: string;
  username: string;
  isVisible: boolean;
  position: { x: number; y: number };
  onClose: () => void;
}

const UserHoverCard: React.FC<UserHoverCardProps> = ({
  userId,
  isVisible,
  position,
  onClose
}) => {
  const { users } = useAuth();
  const { posts } = usePosts();
  const [user, setUser] = useState<User | null>(null);
  const [userPosts, setUserPosts] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (isVisible && userId) {
      setIsLoading(true);

      // Find user data
      const foundUser = users.find(u => u.id === userId);
      setUser(foundUser || null);

      // Count user posts (including anonymous posts they made)
      const postCount = posts.filter(post =>
        post.userId === userId || post.actualUserId === userId
      ).length;
      setUserPosts(postCount);

      setIsLoading(false);
    }
  }, [isVisible, userId, users, posts]);

  if (!isVisible || !user) return null;

  // Calculate position to keep card on screen
  const cardWidth = 320;
  const cardHeight = 200;
  const adjustedX = position.x + cardWidth > window.innerWidth
    ? position.x - cardWidth - 10
    : position.x + 10;
  const adjustedY = position.y + cardHeight > window.innerHeight
    ? position.y - cardHeight - 10
    : position.y + 10;

  return (
    <>
      {/* Invisible overlay to detect mouse leave */}
      <div
        className="fixed inset-0 z-40"
        onMouseEnter={onClose}
      />

      {/* Hover card */}
      <div
        className="fixed z-50 bg-neutral-surface border border-neutral-border rounded-lg shadow-2xl p-4 animate-scale-in hover-glow cyber-border"
        style={{
          left: `${adjustedX}px`,
          top: `${adjustedY}px`,
          width: `${cardWidth}px`,
          maxHeight: `${cardHeight}px`,
        }}
        onMouseLeave={onClose}
      >
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary"></div>
          </div>
        ) : (
          <div className="space-y-3">
            {/* Header with avatar and username */}
            <div className="flex items-center space-x-3">
              <img
                src={user.avatarUrl}
                alt={user.username}
                className="w-12 h-12 rounded-full border-2 border-brand-primary hover-scale transition-transform duration-200"
              />
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-neutral-100 text-lg truncate animate-text-glow">
                  {user.username}
                </h3>
                <div className="flex items-center space-x-1">
                  {user.id === 'admin_s3kt0r_truth' && (
                    <span className="text-xs bg-red-500/20 text-red-400 px-2 py-0.5 rounded-full animate-cyber-flicker">
                      Admin
                    </span>
                  )}
                  {!user.isActive && (
                    <span className="text-xs bg-accent-error/20 text-accent-error px-2 py-0.5 rounded-full">
                      Inactive
                    </span>
                  )}
                  {user.isPendingApproval && (
                    <span className="text-xs bg-accent-warning/20 text-accent-warning px-2 py-0.5 rounded-full">
                      Pending
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Stats */}
            <div className="flex space-x-6 text-center">
              <div>
                <span className="font-bold text-lg text-neutral-100">{userPosts}</span>
                <p className="text-neutral-muted text-xs">Posts</p>
              </div>
              <div>
                <span className="font-bold text-lg text-neutral-100">0</span>
                <p className="text-neutral-muted text-xs">Followers</p>
              </div>
              <div>
                <span className="font-bold text-lg text-neutral-100">0</span>
                <p className="text-neutral-muted text-xs">Following</p>
              </div>
            </div>

            {/* Bio */}
            {user.bio ? (
              <div className="border-t border-neutral-border pt-3">
                <p className="text-neutral-200 text-sm line-clamp-3 whitespace-pre-line leading-relaxed">
                  {user.bio}
                </p>
              </div>
            ) : (
              <div className="border-t border-neutral-border pt-3">
                <p className="text-neutral-muted text-sm italic">
                  No bio available
                </p>
              </div>
            )}

            {/* Quick action hint */}
            <div className="border-t border-neutral-border pt-2">
              <p className="text-xs text-neutral-muted text-center">
                Click username to view full profile
              </p>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default UserHoverCard;
